<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/music_bg"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="160dp">

        <RelativeLayout
            android:id="@+id/rl_title"
            android:layout_width="300dp"
            android:layout_height="70dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/title_bg"
            android:gravity="center_horizontal"
            android:paddingLeft="80dp">

            <TextView
                android:id="@+id/tv_music_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/music_title"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"/>

            <TextView
                android:layout_marginTop="4dp"
                android:id="@+id/tv_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_music_title"
                android:layout_alignLeft="@id/tv_music_title"
                android:text="@string/music_type"
                android:textSize="10sp" />

            <SeekBar
                android:id="@+id/sb"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:layout_below="@id/rl_time"
                android:layout_alignParentBottom="true"
                android:thumb="@null" />

            <RelativeLayout
                android:layout_marginTop="4dp"
                android:id="@+id/rl_time"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_type">

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tv_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:text="00:00"
                    android:textSize="10sp" />
            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="340dp"
            android:layout_height="90dp"
            android:layout_below="@id/rl_title"
            android:layout_centerHorizontal="true"
            android:background="@drawable/btn_bg"
            android:gravity="center_vertical"
            android:paddingLeft="120dp"
            android:paddingRight="10dp">

            <Button
                android:id="@+id/btn_play"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_margin="4dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bg_selector"
                android:text="@string/play"
                android:textColor="#000000"
                android:textSize="12sp"
                android:padding="0dp"/>

            <Button
                android:id="@+id/btn_pause"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_margin="4dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bg_selector"
                android:text="@string/pause"
                android:textColor="#000000"
                android:textSize="12sp"
                android:padding="0dp"/>

            <Button
                android:id="@+id/btn_continue_play"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_margin="4dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bg_selector"
                android:text="@string/continue_play"
                android:textColor="#000000"
                android:textSize="12sp"
                android:padding="0dp"/>

            <Button
                android:id="@+id/btn_exit"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_margin="4dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bg_selector"
                android:text="@string/exit"
                android:textColor="#000000"
                android:textSize="12sp"
                android:padding="0dp"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_music"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="35dp"
            android:layout_marginBottom="50dp"
            android:src="@drawable/img_music" />
    </RelativeLayout>
</LinearLayout>