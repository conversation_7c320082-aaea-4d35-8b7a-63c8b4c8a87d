package com.example.music0615;

import android.app.Service;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import java.util.Timer;
import java.util.TimerTask;

public class MusicService extends Service {
    private MediaPlayer player;
    private Timer timer;
    private static final String TAG = "MusicService";

    public MusicService() {}

    @Override
    public IBinder onBind(Intent intent) {
        return new MusicControl();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        player = new MediaPlayer();
        Log.d(TAG, "MusicService created");
    }

    public void addTimer() {
        if (timer == null) {
            timer = new Timer();
            TimerTask task = new TimerTask() {
                @Override
                public void run() {
                    if (player == null) return;
                    new Handler(Looper.getMainLooper()).post(() -> {
                        try {
                            int duration = player.getDuration();
                            int currentPosition = player.getCurrentPosition();
                            Message msg = MainActivity.handler.obtainMessage();
                            Bundle bundle = new Bundle();
                            bundle.putInt("duration", duration);
                            bundle.putInt("currentPosition", currentPosition);
                            msg.setData(bundle);
                            MainActivity.handler.sendMessage(msg);
                        } catch (IllegalStateException e) {
                            Log.e(TAG, "MediaPlayer状态异常", e);
                        }
                    });
                }
            };
            timer.schedule(task, 5, 500);
        }
    }

    class MusicControl extends Binder {
        public void play() {
            try {
                if (player == null) {
                    player = new MediaPlayer();
                }
                player.reset();
                player = MediaPlayer.create(getApplicationContext(), R.raw.music);
                if (player == null) {
                    Log.e(TAG, "创建MediaPlayer失败: 音频文件可能不存在");
                    return;
                }
                player.start();
                addTimer();
            } catch (Exception e) {
                Log.e(TAG, "播放错误", e);
            }
        }

        public void pausePlay() {
            if (player != null && player.isPlaying()) {
                player.pause();
            }
        }

        public void continuePlay() {
            if (player != null && !player.isPlaying()) {
                player.start();
            }
        }

        public void seekTo(int progress) {
            if (player != null) {
                player.seekTo(progress);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "MusicService destroyed");
        if (player != null) {
            if (player.isPlaying()) {
                player.stop();
            }
            player.release();
            player = null;
        }
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }
}