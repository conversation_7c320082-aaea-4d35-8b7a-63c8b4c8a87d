package com.example.music0615;

import android.animation.ObjectAnimator;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {
    private static SeekBar sb;
    private static TextView tv_progress, tv_total;
    private ObjectAnimator animator;
    private MusicService.MusicControl musicControl;
    private MyServiceConn conn;
    private Intent intent;
    private boolean isUnbind = false;
    private boolean isServiceBound = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        init();

        // 添加绑定状态监听
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isServiceBound) {
                    Toast.makeText(MainActivity.this, R.string.reconnecting_service, Toast.LENGTH_SHORT).show();
                    // 尝试重新绑定
                    if (conn != null) {
                        unbindService(conn); // 先解绑
                    }
                    conn = new MyServiceConn();
                    bindService(intent, conn, BIND_AUTO_CREATE);
                }
            }
        }, 1000); // 1秒后检查
    }

    private void init() {
        tv_progress = findViewById(R.id.tv_progress);
        tv_total = findViewById(R.id.tv_total);
        sb = findViewById(R.id.sb);

        findViewById(R.id.btn_play).setOnClickListener(this);
        findViewById(R.id.btn_pause).setOnClickListener(this);
        findViewById(R.id.btn_continue_play).setOnClickListener(this);
        findViewById(R.id.btn_exit).setOnClickListener(this);

        intent = new Intent(this, MusicService.class);
        startService(intent); // 确保服务启动
        conn = new MyServiceConn();
        bindService(intent, conn, BIND_AUTO_CREATE);

        sb.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (progress == seekBar.getMax()) {
                    if (animator != null) animator.pause();
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (musicControl != null) {
                    int progress = seekBar.getProgress();
                    musicControl.seekTo(progress);
                }
            }
        });

        ImageView iv_music = findViewById(R.id.iv_music);
        animator = ObjectAnimator.ofFloat(iv_music, "rotation", 0f, 360.0f);
        animator.setDuration(10000);
        animator.setInterpolator(new LinearInterpolator());
        animator.setRepeatCount(-1);
    }

    public static Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            Bundle bundle = msg.getData();
            int duration = bundle.getInt("duration");
            int currentPostition = bundle.getInt("currentPosition");
            sb.setMax(duration);
            sb.setProgress(currentPostition);

            int minute = duration / 1000 / 60;
            int second = duration / 1000 % 60;
            String strMinute = minute < 10 ? "0" + minute : minute + "";
            String strSecond = second < 10 ? "0" + second : second + "";
            tv_total.setText(strMinute + ":" + strSecond);

            minute = currentPostition / 1000 / 60;
            second = currentPostition / 1000 % 60;
            strMinute = minute < 10 ? "0" + minute : minute + "";
            strSecond = second < 10 ? "0" + second : second + "";
            tv_progress.setText(strMinute + ":" + strSecond);
        }
    };

    class MyServiceConn implements ServiceConnection {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            musicControl = (MusicService.MusicControl) service;
            isServiceBound = true;
            Toast.makeText(MainActivity.this, R.string.service_connected, Toast.LENGTH_SHORT).show();
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            musicControl = null;
            isServiceBound = false;
        }
    }

    private void unbind(boolean isUnbind) {
        if (!isUnbind && isServiceBound) {
            if (musicControl != null) {
                musicControl.pausePlay();
            }
            unbindService(conn);
            stopService(intent);
            isServiceBound = false;
        }
    }

    @Override
    public void onClick(View v) {
        // 添加服务状态检查
        if (conn == null || !isServiceBound || musicControl == null) {
            Toast.makeText(this, R.string.service_connecting, Toast.LENGTH_SHORT).show();
            // 尝试重新绑定
            if (conn == null) {
                conn = new MyServiceConn();
                bindService(intent, conn, BIND_AUTO_CREATE);
            }
            return;
        }

        int id = v.getId();
        if (id == R.id.btn_play) {
            musicControl.play();
            if (animator != null) animator.start();
        }
        else if (id == R.id.btn_pause) {
            musicControl.pausePlay();
            if (animator != null) animator.pause();
        }
        else if (id == R.id.btn_continue_play) {
            musicControl.continuePlay();
            if (animator != null) animator.start();
        }
        else if (id == R.id.btn_exit) {
            unbind(isUnbind);
            isUnbind = true;
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbind(isUnbind);
    }
}