<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0"/> <!-- 浅灰色背景 -->
            <corners android:radius="4dp"/>
            <stroke android:width="1dp" android:color="#BDBDBD"/>
        </shape>
    </item>
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF"/> <!-- 白色背景 -->
            <corners android:radius="4dp"/>
            <stroke android:width="1dp" android:color="#BDBDBD"/>
        </shape>
    </item>
</selector>
